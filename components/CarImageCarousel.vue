<script setup lang="ts">
import type { CarImage } from '~/api/car/types/CarImage'

const props = defineProps<{
  images: CarImage[]
}>()

const { t } = useI18n()

const currentIndex = ref(0)

const totalImages = computed(() => props.images.length)

const hasImages = computed(() => totalImages.value > 0)

function onSlideChange(index: number) {
  currentIndex.value = index
}
</script>

<template>
  <VParallax class="car-image-carousel">
    <template v-if="hasImages">
      <!-- Image Carousel -->
      <VCarousel
        v-model="currentIndex"
        height="300"
        hide-delimiters
        show-arrows="hover"
        @update:model-value="onSlideChange"
      >
        <VCarouselItem
          v-for="(image, index) in images"
          :key="index"
          :value="index"
        >
          <VImg :src="image.url" :alt="image.label" height="300" cover />
        </VCarouselItem>
      </VCarousel>

      <!-- Image Counter -->
      <div class="image-counter">
        <VChip size="small" color="rgba(0, 0, 0, 0.7)" class="ma-2 text-white">
          <VIcon size="16" class="me-1">mdi-image-multiple</VIcon>
          {{ currentIndex + 1 }} / {{ totalImages }}
        </VChip>
      </div>
    </template>

    <!-- No Images State -->
    <div v-else class="no-images-state">
      <VCard height="300" class="d-flex align-center justify-center">
        <div class="text-center">
          <VIcon size="64" color="grey-lighten-2">mdi-car</VIcon>
          <div class="text-grey-darken-1 mt-2">
            {{ t('No images available') }}
          </div>
        </div>
      </VCard>
    </div>
  </VParallax>
</template>

<style scoped>
.car-image-carousel {
  position: relative;
}

.image-counter {
  position: absolute;
  bottom: 0px;
  z-index: 2;
}

.no-images-state {
  border-radius: 8px;
  overflow: hidden;
}
</style>
