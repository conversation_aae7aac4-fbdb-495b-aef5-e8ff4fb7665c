<script setup lang="ts">
import { useLocalizedBodyTypes } from '~/api/car/enums/BodyType'
import { useLocalizedCities } from '~/api/car/enums/City'
import { useLocalizedCurrencies } from '~/api/car/enums/Currency'
import { useLocalizedDrivetrainSystems } from '~/api/car/enums/DrivetrainSystem'
import { useLocalizedExteriorColors } from '~/api/car/enums/ExteriorColor'
import { useLocalizedFuelTypes } from '~/api/car/enums/FuelType'
import { useLocalizedInteriorColors } from '~/api/car/enums/InteriorColor'
import { useLocalizedRegionalSpecifications } from '~/api/car/enums/RegionalSpecification'
import { useLocalizedStatuses } from '~/api/car/enums/Status'
import { useLocalizedTransmissionTypes } from '~/api/car/enums/TransmissionType'
import { useGetCarQuery } from '~/api/car/queries'

const route = useRoute()
const { t } = useI18n()

const carId = computed(() => Number(route.params.id))

const {
  data: carResponse,
  suspense,
  error,
  isLoading,
} = useGetCarQuery(carId.value)

const car = computed(() => carResponse.value?.data)

// Localized enums
const bodyTypes = useLocalizedBodyTypes()
const cities = useLocalizedCities()
const currencies = useLocalizedCurrencies()
const fuelTypes = useLocalizedFuelTypes()
const regionalSpecifications = useLocalizedRegionalSpecifications()
const statuses = useLocalizedStatuses()
const transmissionTypes = useLocalizedTransmissionTypes()
const interiorColors = useLocalizedInteriorColors()
const exteriorColors = useLocalizedExteriorColors()
const drivetrainSystems = useLocalizedDrivetrainSystems()

// Utility functions
function formatNumber(val: number): string {
  return val.toLocaleString()
}

function formatPrice(price: number, currency: string): string {
  return `${formatNumber(price)} ${currencies[currency as keyof typeof currencies]}`
}

onServerPrefetch(suspense)

// SEO
useHead({
  title: computed(() =>
    car.value
      ? `${car.value.car_manufacturer.name_ar} ${car.value.car_model.name_ar} ${car.value.year}`
      : t('Car Details'),
  ),
})
</script>

<template>
  <VContainer class="py-6">
    <!-- Loading State -->
    <div
      v-if="isLoading"
      class="d-flex justify-center align-center"
      style="min-height: 400px"
    >
      <VProgressCircular indeterminate size="64" color="primary" />
    </div>

    <!-- Error State -->
    <VAlert v-else-if="error" type="error" class="mb-4">
      {{ t('Failed to load car details') }}
    </VAlert>

    <!-- Car Details -->
    <div v-else-if="car">
      <!-- Header Section -->
      <VRow class="mb-6">
        <VCol cols="12">
          <div class="d-flex align-center mb-4">
            <VBtn
              icon="mdi-arrow-left"
              variant="text"
              class="me-3"
              @click="$router.back()"
            />
            <div>
              <h1 class="text-h4 font-weight-bold">
                {{ car.car_manufacturer.name_ar }} {{ car.car_model.name_ar }}
              </h1>
              <div class="text-subtitle-1 text-grey-darken-1">
                {{ car.year }} •
                {{ regionalSpecifications[car.regional_specifications] }}
              </div>
            </div>
          </div>
        </VCol>
      </VRow>

      <VRow>
        <!-- Images Section -->
        <VCol cols="12" md="8">
          <VCard elevation="2" rounded="lg" class="mb-4">
            <template v-if="car.images.length">
              <!-- Main Image -->
              <VImg
                :src="car.images[0].url"
                height="400"
                cover
                class="rounded-t-lg"
              />

              <!-- Image Thumbnails -->
              <VCardText v-if="car.images.length > 1" class="pa-3">
                <VRow no-gutters>
                  <VCol
                    v-for="(image, index) in car.images.slice(1)"
                    :key="index"
                    cols="3"
                    sm="2"
                    class="pa-1"
                  >
                    <VImg
                      :src="image.url"
                      height="80"
                      cover
                      class="rounded cursor-pointer"
                      @click="
                        car.images.unshift(car.images.splice(index + 1, 1)[0])
                      "
                    />
                  </VCol>
                </VRow>
              </VCardText>
            </template>
            <VCardText v-else class="text-center py-8">
              <VIcon size="64" color="grey-lighten-2">mdi-car</VIcon>
              <div class="text-grey-darken-1 mt-2">
                {{ t('No images available') }}
              </div>
            </VCardText>
          </VCard>
        </VCol>

        <!-- Details Section -->
        <VCol cols="12" md="4">
          <!-- Price Card -->
          <VCard elevation="2" rounded="lg" class="mb-4">
            <VCardText>
              <div class="text-h4 font-weight-bold text-primary mb-2">
                {{ formatPrice(car.price, car.currency) }}
              </div>
              <VChip
                :color="car.status === 'new' ? 'success' : 'warning'"
                size="small"
                class="mb-3"
              >
                {{ statuses[car.status] }}
              </VChip>

              <!-- Action Buttons -->
              <div class="d-flex flex-column gap-2">
                <VBtn
                  color="primary"
                  size="large"
                  prepend-icon="mdi-phone"
                  block
                >
                  {{ t('Call Store') }}
                </VBtn>
                <VBtn
                  color="success"
                  size="large"
                  prepend-icon="mdi-whatsapp"
                  variant="outlined"
                  block
                >
                  {{ t('WhatsApp') }}
                </VBtn>
              </div>
            </VCardText>
          </VCard>

          <!-- Store Information -->
          <VCard elevation="2" rounded="lg" class="mb-4">
            <VCardTitle>{{ t('Store Information') }}</VCardTitle>
            <VCardText>
              <div class="d-flex align-center mb-2">
                <VIcon class="me-2" color="primary">mdi-store</VIcon>
                <span class="font-weight-medium">{{ car.store.name_ar }}</span>
              </div>
              <div class="d-flex align-center">
                <VIcon class="me-2" color="primary">mdi-map-marker</VIcon>
                <span>{{ cities[car.city] }}</span>
              </div>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>

      <!-- Specifications Section -->
      <VRow class="mt-4">
        <VCol cols="12">
          <VCard elevation="2" rounded="lg">
            <VCardTitle>{{ t('Specifications') }}</VCardTitle>
            <VCardText>
              <VRow>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Body Type') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ bodyTypes[car.body_type] }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Fuel Type') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ fuelTypes[car.fuel_type] }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Transmission') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ transmissionTypes[car.transmission] }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Drivetrain') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ drivetrainSystems[car.drivetrain_system] }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Engine Capacity') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ car.engine_capacity }}L
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Horsepower') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ car.horse_power }} HP
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Cylinders') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ car.cylinder_count }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Seats') }}
                    </div>
                    <div class="font-weight-medium">{{ car.seats_count }}</div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Exterior Color') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ exteriorColors[car.exterior_color] }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Interior Color') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ interiorColors[car.interior_color] }}
                    </div>
                  </div>
                </VCol>
                <VCol v-if="car.status === 'used'" cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Mileage') }}
                    </div>
                    <div class="font-weight-medium">
                      {{ formatNumber(car.travel_distance_in_km) }}
                      {{ t('enums.units.km.short') }}
                    </div>
                  </div>
                </VCol>
                <VCol cols="12" sm="6" md="4">
                  <div class="mb-4">
                    <div class="text-caption text-grey-darken-1">
                      {{ t('Warranty') }}
                    </div>
                    <div class="font-weight-medium">
                      <VIcon
                        :color="car.has_warranty ? 'success' : 'error'"
                        size="small"
                        class="me-1"
                      >
                        {{
                          car.has_warranty
                            ? 'mdi-check-circle'
                            : 'mdi-close-circle'
                        }}
                      </VIcon>
                      {{ car.has_warranty ? t('Yes') : t('No') }}
                    </div>
                  </div>
                </VCol>
              </VRow>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </div>

    <!-- Not Found State -->
    <VAlert v-else type="warning" class="text-center">
      {{ t('Car not found') }}
    </VAlert>
  </VContainer>
</template>
