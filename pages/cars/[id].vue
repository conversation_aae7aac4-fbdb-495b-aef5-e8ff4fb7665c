<script setup lang="ts">
import { useLocalizedBodyTypes } from '~/api/car/enums/BodyType'
import { useLocalizedCities } from '~/api/car/enums/City'
import type { Currency } from '~/api/car/enums/Currency'
import { useLocalizedCurrencies } from '~/api/car/enums/Currency'
import { useLocalizedDrivetrainSystems } from '~/api/car/enums/DrivetrainSystem'
import { useLocalizedExteriorColors } from '~/api/car/enums/ExteriorColor'
import { useLocalizedFeatureCategories } from '~/api/car/enums/FeatureCategory'
import { useLocalizedFuelTypes } from '~/api/car/enums/FuelType'
import { useLocalizedInteriorColors } from '~/api/car/enums/InteriorColor'
import { useLocalizedRegionalSpecifications } from '~/api/car/enums/RegionalSpecification'
import { useLocalizedTransmissionTypes } from '~/api/car/enums/TransmissionType'
import { useGetCarQuery } from '~/api/car/queries'
import type { Car } from '~/api/car/types/Car'
import type { Feature } from '~/api/car/types/Feature'

const route = useRoute()
const { t } = useI18n()

const carId = computed(() => Number(route.params.id))

const {
  data: carResponse,
  suspense,
  error,
  isLoading,
} = useGetCarQuery(carId.value)

const car = computed<Car>(() => carResponse.value?.data)

// Localized enums
const bodyTypes = useLocalizedBodyTypes()
const cities = useLocalizedCities()
const currencies = useLocalizedCurrencies()
const fuelTypes = useLocalizedFuelTypes()
const regionalSpecifications = useLocalizedRegionalSpecifications()
const transmissionTypes = useLocalizedTransmissionTypes()
const interiorColors = useLocalizedInteriorColors()
const exteriorColors = useLocalizedExteriorColors()
const drivetrainSystems = useLocalizedDrivetrainSystems()
const featureCategories = useLocalizedFeatureCategories()

// Utility functions
function formatNumber(val: number): string {
  return val.toLocaleString()
}

const formattedPrice = computed(
  () =>
    `${car.value.price.toLocaleString()} ${currencies[car.value.currency as Currency]}`,
)

// Specifications data with icons
const specifications = computed(() => {
  if (!car.value) return []

  return [
    {
      key: 'Body Type',
      value: bodyTypes[car.value.body_type],
      icon: 'mdi-car-side',
    },
    {
      key: 'Fuel Type',
      value: fuelTypes[car.value.fuel_type],
      icon: 'mdi-gas-station',
    },
    {
      key: 'Transmission',
      value: transmissionTypes[car.value.transmission],
      icon: 'mdi-car-shift-pattern',
    },
    {
      key: 'Drivetrain',
      value: drivetrainSystems[car.value.drivetrain_system],
      icon: 'mdi-car-4wd',
    },
    {
      key: 'Engine Capacity',
      value: `${car.value.engine_capacity}L`,
      icon: 'mdi-engine',
    },
    {
      key: 'Horsepower',
      value: `${car.value.horse_power} HP`,
      icon: 'mdi-speedometer',
    },
    {
      key: 'Cylinders',
      value: car.value.cylinder_count.toString(),
      icon: 'mdi-cylinder',
    },
    {
      key: 'Seats',
      value: car.value.seats_count.toString(),
      icon: 'mdi-car-seat',
    },
    {
      key: 'Exterior Color',
      value: exteriorColors[car.value.exterior_color],
      icon: 'mdi-palette',
    },
    {
      key: 'Interior Color',
      value: interiorColors[car.value.interior_color],
      icon: 'mdi-car-seat-outline',
    },
    {
      key: 'Warranty',
      value: car.value.has_warranty ? t('Yes') : t('No'),
      icon: car.value.has_warranty ? 'mdi-check-circle' : 'mdi-close-circle',
      iconColor: car.value.has_warranty ? 'success' : 'error',
    },
  ]
})

const groupedFeatures = computed(() => {
  if (!car.value?.features) return {}

  return car.value.features.reduce(
    (acc: Record<string, Feature[]>, feature: Feature) => {
      if (!acc[feature.category]) {
        acc[feature.category] = []
      }
      acc[feature.category].push(feature)
      return acc
    },
    {} as Record<string, Feature[]>,
  )
})

onServerPrefetch(suspense)

// SEO
useHead({
  title: computed(() =>
    car.value
      ? `${car.value.car_manufacturer.name_ar} ${car.value.car_model.name_ar} ${car.value.year}`
      : t('Car Details'),
  ),
})
</script>

<template>
  <!-- Loading State -->
  <div
    v-if="isLoading"
    class="d-flex justify-center align-center"
    style="min-height: 400px"
  >
    <VProgressCircular indeterminate size="64" color="primary" />
  </div>

  <!-- Error State -->
  <VAlert v-else-if="error" type="error" class="ma-4">
    {{ t('Failed to load car details') }}
  </VAlert>

  <!-- Car Details -->
  <div v-else-if="car" class="car-details-mobile bg-surface">
    <!-- Car Image Carousel -->
    <CarImageCarousel :images="car.images" />

    <!-- Price Section -->
    <div class="pa-4">
      <VCard elevation="0" color="transparent">
        <VCardText class="pa-0">
          <!-- Price Block -->
          <div class="text-h3 font-weight-bold text-primary mb-2">
            {{ formattedPrice }}
          </div>

          <!-- Car Name -->
          <div class="text-h6 font-weight-medium text-grey-darken-2">
            {{ car.car_manufacturer.name_ar }} {{ car.car_model.name_ar }}
          </div>
        </VCardText>
      </VCard>
    </div>

    <!-- Quick Info Icons Row -->
    <div class="pa-4 pt-0">
      <VRow no-gutters class="mb-3">
        <VCol cols="4" class="text-center">
          <div v-tooltip:bottom="t('Year')" class="d-flex align-center">
            <VIcon color="primary" size="24" class="me-1">mdi-calendar</VIcon>
            <div class="text-body-2 font-weight-bold">{{ car.year }}</div>
          </div>
        </VCol>
        <VCol v-if="car.status === 'used'" cols="4" class="text-center">
          <div v-tooltip:bottom="t('Mileage')" class="d-flex align-center">
            <VIcon color="primary" size="24" class="me-1"
              >mdi-speedometer</VIcon
            >
            <div class="text-body-2 font-weight-bold">
              {{ formatNumber(car.travel_distance_in_km) }}
              {{ t('enums.units.km.short') }}
            </div>
          </div>
        </VCol>
        <VCol :cols="car.status === 'used' ? 4 : 8" class="text-center">
          <div
            v-tooltip:bottom="t('Regional Specs')"
            class="d-flex align-center"
          >
            <VIcon color="primary" size="24" class="me-1">mdi-earth</VIcon>
            <div class="text-body-2 font-weight-bold">
              {{ regionalSpecifications[car.regional_specifications] }}
            </div>
          </div>
        </VCol>
      </VRow>
    </div>

    <VDivider />

    <!-- Specifications Section -->
    <div class="pa-4">
      <h3 class="text-h6 font-weight-bold mb-4">{{ t('Specifications') }}</h3>

      <div class="specifications-list">
        <template v-for="(spec, index) in specifications" :key="spec.key">
          <div class="d-flex justify-space-between align-center py-2">
            <div class="d-flex align-center">
              <VIcon
                :color="spec.iconColor || 'primary'"
                size="20"
                class="me-2"
              >
                {{ spec.icon }}
              </VIcon>
              <span class="text-grey-darken-1">{{ t(spec.key) }}</span>
            </div>
            <span class="font-weight-medium">{{ spec.value }}</span>
          </div>
          <VDivider v-if="index < specifications.length - 1" />
        </template>
      </div>

      <VDivider class="mt-4" />
    </div>
    <!-- Features Section -->
    <div v-if="Object.keys(groupedFeatures).length > 0" class="pa-4">
      <h3 class="text-h6 font-weight-bold mb-4">{{ t('Features') }}</h3>

      <VExpansionPanels variant="accordion" class="mb-4">
        <VExpansionPanel
          v-for="(features, category) in groupedFeatures"
          :key="category"
          :title="featureCategories[category]"
        >
          <VExpansionPanelText>
            <div class="features-list">
              <div
                v-for="feature in features"
                :key="feature.name_ar"
                class="d-flex align-center py-1"
              >
                <VIcon size="16" color="success" class="me-2">mdi-check</VIcon>
                <span>{{ feature.name_ar }}</span>
              </div>
            </div>
          </VExpansionPanelText>
        </VExpansionPanel>
      </VExpansionPanels>

      <VDivider />
    </div>

    <!-- Store Information Section -->
    <div class="pa-4">
      <h3 class="text-h6 font-weight-bold mb-4">
        {{ t('Store Information') }}
      </h3>

      <VCard elevation="1" rounded="lg" class="mb-4">
        <VCardText>
          <!-- Store Image Placeholder -->
          <div class="store-image-placeholder mb-3">
            <VCard
              height="120"
              color="grey-lighten-4"
              class="d-flex align-center justify-center"
            >
              <div class="text-center">
                <VIcon size="48" color="grey-lighten-1">mdi-store</VIcon>
                <div class="text-caption text-grey-darken-1 mt-1">
                  {{ t('Store Image') }}
                </div>
              </div>
            </VCard>
          </div>

          <!-- Store Details -->
          <div class="mb-3">
            <div class="text-h6 font-weight-medium mb-1">
              {{ car.store.name_ar }}
            </div>
            <div class="d-flex align-center text-grey-darken-1">
              <VIcon size="16" class="me-1">mdi-map-marker</VIcon>
              <span>{{ cities[car.city] }}</span>
            </div>
          </div>

          <!-- Show All Cars Button -->
          <VBtn
            color="primary"
            variant="outlined"
            block
            class="mb-3"
            @click="navigateTo(`/stores/${car.store_id}/cars`)"
          >
            {{ t('Show All Cars from This Store') }}
          </VBtn>

          <!-- Map Placeholder -->
          <div class="map-placeholder">
            <VCard
              height="150"
              color="grey-lighten-4"
              class="d-flex align-center justify-center"
            >
              <div class="text-center">
                <VIcon size="48" color="grey-lighten-1">mdi-map</VIcon>
                <div class="text-caption text-grey-darken-1 mt-1">
                  {{ t('Store Location Map') }}
                </div>
                <div class="text-caption text-grey-darken-2">
                  {{ t('Coming Soon') }}
                </div>
              </div>
            </VCard>
          </div>
        </VCardText>
      </VCard>

      <!-- Action Buttons -->
      <div class="d-flex flex-column gap-3">
        <VBtn color="primary" size="large" prepend-icon="mdi-phone" block>
          {{ t('Call Store') }}
        </VBtn>
        <VBtn
          color="success"
          size="large"
          prepend-icon="mdi-whatsapp"
          variant="outlined"
          block
        >
          {{ t('WhatsApp') }}
        </VBtn>
      </div>
    </div>
  </div>

  <!-- Not Found State -->
  <VAlert v-else type="warning" class="text-center ma-4">
    {{ t('Car not found') }}
  </VAlert>
</template>

<style scoped>
.car-details-mobile {
  max-width: 100%;
  background-color: #fafafa;
  min-height: 100vh;
}

.specifications-list {
  background-color: white;
  border-radius: 8px;
  padding: 8px 0;
}

.features-list {
  padding: 8px 0;
}

.store-image-placeholder,
.map-placeholder {
  border-radius: 8px;
  overflow: hidden;
}

/* Mobile-first responsive adjustments */
@media (min-width: 768px) {
  .car-details-mobile {
    max-width: 600px;
    margin: 0 auto;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
