import { useQuery } from '@tanstack/vue-query'
import { getCarById, getPaginatedCars } from './api'
import type { Car } from './types/Car'

const BASE_QUERY_KEY = 'cars'

export const useGetPaginatedCarsQuery = () =>
  useQuery({
    queryKey: [BASE_QUERY_KEY],
    queryFn: getPaginatedCars,
  })

export const useGetCarQuery = (id: Car['id']) =>
  useQuery({
    queryKey: [BASE_QUERY_KEY, id],
    queryFn: () => getCarById(id),
    enabled: !!id,
  })
