import type { Car, PaginatedCar } from '~/api/car/types/Car'
import { makeApiRequest } from '~/api/client'
import type { ApiResponse } from '~/api/types/ApiResponse'
import type { PaginatedResponse } from '~/api/types/PaginatedResponse'

export const getPaginatedCars = async (): Promise<
  PaginatedResponse<PaginatedCar>
> => makeApiRequest<PaginatedResponse<PaginatedCar>>('/cars')

export const getCarById = async (id: Car['id']): Promise<ApiResponse<Car>> =>
  makeApiRequest<ApiResponse<Car>>(`/cars/${id}`)
