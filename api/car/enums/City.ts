import { useI18n } from 'vue-i18n'

const cityValues = [
  'damascus',
  'aleppo',
  'homs',
  'hama',
  'latakia',
  'tartus',
  'idlib',
  'deir-ez-zor',
  'raqqa',
  'hasakah',
  'suwayda',
  'daraa',
  'quneitra',
  'rif-dimashq',
] as const

export const useLocalizedCities = () => {
  const { t } = useI18n()
  return cityValues.reduce(
    (acc, value) => {
      acc[value] = t(`enums.city.${value}`)
      return acc
    },
    {} as Record<(typeof cityValues)[number], string>,
  )
}

export type City = (typeof cityValues)[number]
