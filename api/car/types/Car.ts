import type { BodyType } from '~/api/car/enums/BodyType'
import type { City } from '~/api/car/enums/City'
import type { Currency } from '~/api/car/enums/Currency'
import type { DrivetrainSystem } from '~/api/car/enums/DrivetrainSystem'
import type { ExteriorColor } from '~/api/car/enums/ExteriorColor'
import type { FuelType } from '~/api/car/enums/FuelType'
import type { InteriorColor } from '~/api/car/enums/InteriorColor'
import type { RegionalSpecification } from '~/api/car/enums/RegionalSpecification'
import type { Status } from '~/api/car/enums/Status'
import type { TransmissionType } from '~/api/car/enums/TransmissionType'
import type { CarImage } from '~/api/car/types/CarImage'
import type { CarManufacturer } from '~/api/car/types/CarManufacturer'
import type { CarModel } from '~/api/car/types/CarModel'
import type { Store } from '~/api/store/types/Store'

export interface Car {
  id: number
  store_id: number
  store: Pick<Store, 'id' | 'name_ar' | 'name_en'>
  car_manufacturer_id: number
  car_manufacturer: CarManufacturer
  car_model_id: number
  car_model: CarModel
  cylinder_count: number
  regional_specifications: RegionalSpecification
  year: number
  travel_distance_in_km: number
  body_type: BodyType
  city: City
  fuel_type: FuelType
  transmission: TransmissionType
  drivetrain_system: DrivetrainSystem
  seats_count: number
  exterior_color: ExteriorColor
  interior_color: InteriorColor
  price: number
  currency: Currency
  status: Status
  engine_capacity: number
  horse_power: number
  has_warranty: boolean
  images_count: number
  preview_images: CarImage[]
  images: CarImage[]
  features: Feature[]
}

export type PaginatedCar = Omit<Car, 'images' | 'features'>
